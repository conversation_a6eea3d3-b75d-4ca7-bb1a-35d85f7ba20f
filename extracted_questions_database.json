{"metadata": {"extraction_date": "2024-06-24", "source": "cameroongcerevision.com", "total_papers_found": 15, "total_questions_extracted": 85, "years_covered": [2019, 2021, 2022, 2023, 2024], "levels": ["o-level", "a-level"], "papers_per_level": [1, 2, 3], "extraction_method": "Direct web scraping from individual paper pages", "papers_successfully_extracted": ["O-Level 2024 Paper 1 (MCQ)", "O-Level 2024 Paper 2 (Structural)", "O-Level 2024 Paper 3 (Practical)", "O-Level 2023 Paper 2 (Structural)", "O-Level 2022 Paper 2 (Structural)", "O-Level 2021 Paper 2 (Structural)", "O-Level 2019 Paper 2 (Structural)", "A-Level 2024 Paper 1 (MCQ)", "A-Level 2024 Paper 2 (Structural)", "A-Level 2023 Paper 2 (Structural)", "A-Level 2019 Paper 1 (MCQ)", "A-Level 2019 Paper 3 (Practical)"]}, "o_level_questions": {"2024": {"paper_1_mcq": [{"id": "OL_2024_P1_Q1", "question": "Browsers are used to interpret HTML code.", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "D. <PERSON>"], "answer": "A", "topic": "Web Technologies", "difficulty": "Easy"}, {"id": "OL_2024_P1_Q2", "question": "The spreadsheet cell ranges A1-E5 has ___ number of cells.", "options": ["A. 25", "B. 30", "C. 5", "D. 15"], "answer": "A", "topic": "Software Applications", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q3", "question": "Arranging storage units from smallest to largest capacity:", "options": ["<PERSON><PERSON>, Kilobyte, Byte, Bit", "B. <PERSON>, Terabyte, Byte, Bit", "C. Bit, Byte, Kilobyte, Terabyte", "D. <PERSON>, Bit, Kilobyte, Terabyte"], "answer": "C", "topic": "Data Representation", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q4", "question": "The result of the binary subtraction 10101₂ - 1111₂ is:", "options": ["A. 1100", "B. 0110", "C. 1000", "D. 1010"], "answer": "B", "topic": "Data Representation", "difficulty": "Hard"}, {"id": "OL_2024_P1_Q5", "question": "At Tinasoft Cybercafe, all computers are connected to a computer in the control room. This type of network architecture is known as:", "options": ["<PERSON><PERSON>eer-to-peer", "B. Proxy-server", "C. Client-server", "<PERSON><PERSON>eer-to-server"], "answer": "C", "topic": "Networks", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q6", "question": "The control structure that enables the computer to repeat processes is called:", "options": ["<PERSON><PERSON>", "B. Iteration", "<PERSON><PERSON>", "D. <PERSON>"], "answer": "B", "topic": "Programming", "difficulty": "Easy"}, {"id": "OL_2024_P1_Q7", "question": "Which of the following is NOT a characteristic of a good Algorithm?", "options": ["A. Pre<PERSON>", "B. Ambiguous", "<PERSON><PERSON> Takes finite time", "D. Logical flow"], "answer": "B", "topic": "Programming", "difficulty": "Easy"}, {"id": "OL_2024_P1_Q8", "question": "A validation method to ensure that data has been received correctly is the ___ check.", "options": ["<PERSON><PERSON>", "B. Length", "<PERSON>. <PERSON>", "<PERSON><PERSON>"], "answer": "D", "topic": "Security", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q9", "question": "Given the web address https://www.cgceboard.cm/homepage.html. Which portion is the domain name?", "options": ["A. https", "B. cgceboard.cm", "C. homepage.html", "D. www"], "answer": "B", "topic": "Web Technologies", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q10", "question": "Signals from a transmission station are being faintly received by a user. A ___ is needed to boost it.", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "C. Gateway", "D. <PERSON>r"], "answer": "B", "topic": "Networks", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q11", "question": "Technology now enables users to send images over telephone lines using a:", "options": ["<PERSON><PERSON>", "B. Fax", "C. Telex", "D. Fixed phone"], "answer": "B", "topic": "Networks", "difficulty": "Easy"}, {"id": "OL_2024_P1_Q12", "question": "DBMS is an example of ___ software.", "options": ["A. System", "B. Application", "C. Operating system", "D. Utility"], "answer": "B", "topic": "Software", "difficulty": "Medium"}, {"id": "OL_2024_P1_Q13", "question": "A malware that looks legitimate and it is hidden within another program is a:", "options": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>. <PERSON> horse", "D. <PERSON>"], "answer": "C", "topic": "Security", "difficulty": "Medium"}], "paper_2_structural": [{"id": "OL_2024_P2_Q1", "question": "A technician wants to set up a local area network (LAN). Name four basic networking equipment that can be used and the principal function of each of them. The technician decides to set up a client-server network. Give two advantages of the technician's choice.", "marks": 8, "topic": "Networks", "difficulty": "Medium"}, {"id": "OL_2024_P2_Q2", "question": "Briefly explain the following computing terms and state an example of each: (a) Integrated Development Environment (b) Compiled language (c) Interpreted language", "marks": 6, "topic": "Programming", "difficulty": "Medium"}, {"id": "OL_2024_P2_Q3", "question": "In the <NAME_EMAIL> identify the username and the domain name. What is the difference between the Internet and the WWW?", "marks": 4, "topic": "Web Technologies", "difficulty": "Easy"}, {"id": "OL_2024_P2_Q4", "question": "<PERSON> is employed as a systems analyst in iCom Enterprise. <PERSON> works regularly from her home and submits her work to her manager and also receives feedback from her manager. What is the term used to describe this work pattern? Give two possible reasons that <PERSON> might use to justify her work pattern. What are the activities of <PERSON> at iCom Enterprise? Name and explain two computer network devices that can facilitate her work.", "marks": 10, "topic": "Networks", "difficulty": "Medium"}, {"id": "OL_2024_P2_Q5", "question": "A school acquires the following items for its multimedia centre: CRT monitors, LCD monitors, RAM chips, HDD, USB keyboards, CPU chips, scanners and cameras. Copy and complete the table below using the items above: Input devices | Output devices | Storage devices | Processing devices. Write the following abbreviations in full: RAM, LCD, CRT, HDD and USB. Briefly explain the functions of RAM and Scanners. HDD is said to be read/write. Briefly explain what is meant by read/write.", "marks": 12, "topic": "Computer Hardware", "difficulty": "Medium"}, {"id": "OL_2024_P2_Q6", "question": "Study the algorithm below and answer the questions that follow: 1. Start 2. Set pi to 3.14 3. Get the radius, R 4. If radius is less than 7, then Result = Pi * R * R 5. <PERSON><PERSON> Result = 2 * Pi * R 6. Print Result 7. <PERSON>. Briefly describe the problem that the algorithm is solving. Represent the algorithm using a flowchart. Compute the result if R = 10.", "marks": 8, "topic": "Programming", "difficulty": "Medium"}, {"id": "OL_2024_P2_Q7", "question": "Write out the full meaning of the following health problems related to computer use: RSI and CVS. State two ways in which RSI and CVS can each be minimised.", "marks": 6, "topic": "Health and Safety", "difficulty": "Easy"}, {"id": "OL_2024_P2_Q8", "question": "Convert the hexadecimal number C7B to the octal scale.", "marks": 4, "topic": "Data Representation", "difficulty": "Hard"}], "paper_3_practical": [{"id": "OL_2024_P3_Q1", "question": "Social media verification: assessing sources and visual content. Do all the tasks (Task I, Task II and Task III) specified in this question paper. Task 1 (15 marks): Social media has changed journalism practice. Real-time audience engagement has given rise to crowdsourcing content, and even reporting tasks like verification can now be outsourced to the audience. Today, eyewitness accounts and visual content are amongst the most important and compelling tools a journalist or news publisher can draw on to tell a high impact story. In a breaking news scenario, speed is a critical factor in verifying information from social media.", "marks": 15, "topic": "Practical Applications", "difficulty": "Medium"}]}, "2023": {"paper_2_structural": [{"id": "OL_2023_P2_Q1", "question": "Long term use of computer equipment can lead to health problems. State one health problem that may result from: (i) Long term use of the keyboard. (ii) Long term use of the screen. (iii) What problem does ergonomics address? (iv) How can ergonomics be applied in order to solve the problem of (i) and (ii) above?", "marks": 4, "topic": "Health and Safety", "difficulty": "Easy"}, {"id": "OL_2023_P2_Q2", "question": "Consider the following Boolean expression Q = (A . B) + (A + B). Draw the logic circuit that corresponds to the expression of <PERSON><PERSON> and complete the truth table. What does a truth table represent?", "marks": 6, "topic": "Logic Circuits", "difficulty": "Medium"}, {"id": "OL_2023_P2_Q3", "question": "Briefly explain each of the following: (a) Computer network (b) Computer ethics (c) Network protocol", "marks": 6, "topic": "Networks", "difficulty": "Medium"}, {"id": "OL_2023_P2_Q4", "question": "With the aid of a sketch, briefly explain the difference between tracks and sectors on a magnetic disk.", "marks": 4, "topic": "Computer Hardware", "difficulty": "Medium"}]}, "2022": {"paper_2_structural": [{"id": "OL_2022_P2_Q1", "question": "An inter-urban transport company carries out the following activities amongst others: cash office collects transport fares from clients; clients pay fare online using electronic money transfer; clients make travel reservations; workers are paid every two weeks; correspondences are sent to partners of the transport company; buses and their respective drivers are scheduled on weekly basis. State four (4) transactions of the company. State the type of application software that can be used to computerize each transaction. Give an example of an application software that can be used for each transaction. How can graphics software be used to improve upon the business? Explain two ways that the company can use the Internet to improve upon the business. Briefly describe one method each that this company could apply to combat: unauthorised access to the computer systems, unauthorised use of files.", "marks": 20, "topic": "Software Applications", "difficulty": "Hard"}]}, "2019": {"paper_2_structural": [{"id": "OL_2019_P2_Q1", "question": "State any two positive impacts in each case of Computer Information System on the following areas: (a) Banking (b) Managing a School Library (c) Commerce", "marks": 6, "topic": "Computer Applications", "difficulty": "Medium"}, {"id": "OL_2019_P2_Q2", "question": "Explain what you understand by the following: (a) System Software (b) Operating System (c) Device drivers (d) Utility software", "marks": 8, "topic": "Software", "difficulty": "Medium"}]}, "2021": {"paper_2_structural": [{"id": "OL_2021_P2_Q1", "question": "Give the role of each of the following types of system buses: (a) Address bus (b) Control bus (c) Data bus", "marks": 6, "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "OL_2021_P2_Q2", "question": "Briefly describe the characteristics of a compact disc (CD) in terms of: (a) How data is stored (b) The physical layout of tracks and sectors on the CD.", "marks": 6, "topic": "Computer Hardware", "difficulty": "Medium"}, {"id": "OL_2021_P2_Q3", "question": "Sketch and label a block diagram of a simplified computer system, that shows the stages of information processing. Describe the main activity of each stage and give one example of a device used.", "marks": 10, "topic": "Computer Hardware", "difficulty": "Medium"}]}}, "a_level_questions": {"2024": {"paper_1_mcq": [{"id": "AL_2024_P1_Q1", "question": "It uses the same instruction sequences to process in the same way large amounts of data streamed to it. At best it must be:", "options": ["A. Multiple-instruction, single-data", "B. Multiple-instruction, multiple-data", "C. Single-instruction, single-data", "D. Single-instruction, multiple-data"], "answer": "D", "topic": "Advanced Computing", "difficulty": "Hard"}, {"id": "AL_2024_P1_Q2", "question": "<PERSON><PERSON> has data to process, at the same time, on powerful machines in various parts of the world. It is best he does this on a(n):", "options": ["A. Online Computer", "B. Distributed Computer", "C. Mainframe Computer", "<PERSON><PERSON>"], "answer": "B", "topic": "Advanced Computing", "difficulty": "Medium"}, {"id": "AL_2024_P1_Q3", "question": "We seek it for long-term storage, temporary storage for executing instructions and data, and for sheer bulk. It is a:", "options": ["<PERSON><PERSON> Main memory", "<PERSON><PERSON> Hard drive", "<PERSON><PERSON> memory", "D. USB drives"], "answer": "B", "topic": "Computer Hardware", "difficulty": "Medium"}, {"id": "AL_2024_P1_Q4", "question": "What is the result of the following in ones complement notation? +10110011 - 10001010.", "options": ["A. 00101001", "B. 00101000", "C. 10101001", "D. 10011000"], "answer": "A", "topic": "Data Representation", "difficulty": "Hard"}, {"id": "AL_2024_P1_Q5", "question": "What guides a machine on how to execute any instruction given it?", "options": ["A. The values to use in operations", "B. The machine addressing mode", "C. The machine instruction set", "D. The instruction format"], "answer": "C", "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "AL_2024_P1_Q6", "question": "A machine's CPU takes arguments for its binary operations from the accumulator. It surely is a:", "options": ["A. Three-address machine", "B. Two-address machine", "C. One-address machine", "D. Zero-address machine"], "answer": "C", "topic": "Computer Architecture", "difficulty": "Hard"}, {"id": "AL_2024_P1_Q7", "question": "In indexed mode addressing of machines:", "options": ["A. The operand of the instruction is not used immediately", "B. The operand is an offset to the location where the sought value is found", "C. The operand field holds an address for its immediate use", "D. The operand address is given as an offset from the instruction"], "answer": "B", "topic": "Computer Architecture", "difficulty": "Hard"}, {"id": "AL_2024_P1_Q8", "question": "It is used for work, for teaching and learning, without preliminary training, for instant feedback and suitable displays. It must be a(n):", "options": ["A. Video Conferencing System", "B. Email system", "C. Information retrieval system", "D. Office automation system"], "answer": "C", "topic": "Advanced Systems", "difficulty": "Medium"}, {"id": "AL_2024_P1_Q9", "question": "It is good at repetitive tasks, a good replacement for humans in hostile environments. It must be:", "options": ["A. Computer-aided Manufacturing", "<PERSON><PERSON> An intelligent machine", "C. A Simulation Game", "D. A robot"], "answer": "D", "topic": "Advanced Computing", "difficulty": "Easy"}, {"id": "AL_2024_P1_Q10", "question": "It is good for incidents reporting, tracking evolution over time, across distances, and must manage large amounts of data.", "options": ["A. Database Management Systems", "B. Information & Communication Systems", "C. Geographic Information Systems", "D. Management Information Systems"], "answer": "C", "topic": "Advanced Systems", "difficulty": "Medium"}], "paper_2_structural": [{"id": "AL_2024_P2_Q1", "question": "Using two's complement, show how the following operations on the denary numbers could be performed in a 4-bit register: (a) 5 - 4 (b) -3 + 6", "marks": 8, "topic": "Data Representation", "difficulty": "Hard"}, {"id": "AL_2024_P2_Q2", "question": "Express the AND gate and the OR gate in terms of NAND gates only.", "marks": 4, "topic": "Logic Circuits", "difficulty": "Medium"}, {"id": "AL_2024_P2_Q3", "question": "Explain how the width of the data bus and system clock speed affect the performance of a computer system.", "marks": 3, "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "AL_2024_P2_Q4", "question": "The table below shows six stages in the fetch-execute cycle in a Von Neumann computer. Put the stages into the correct sequence by writing the numbers 1 to 6 in the right-hand column. In your answer booklet, reproduce this table, replacing each description on the left with its corresponding letter (A to F).", "marks": 3, "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "AL_2024_P2_Q5", "question": "Perform a 2 place logical right shift on the binary number 11001011. Convert the binary number obtained to decimal and state the effect of performing a 2 place logical right shift on the binary number.", "marks": 3, "topic": "Data Representation", "difficulty": "Medium"}, {"id": "AL_2024_P2_Q6", "question": "Consider the following assignment statement written in a high-level language, where A is an array, = the assignment operator and array indexing starts with 0. A[3] = A[2] + A[1]. Explain what is meant by an addressing mode. What is absolute addressing? Using register and indexed addressing modes only, show how you would implement the above statement in assembly.", "marks": 8, "topic": "Computer Architecture", "difficulty": "Hard"}]}, "2023": {"paper_2_structural": [{"id": "AL_2023_P2_Q1", "question": "Which of direct access and sequential access takes a longer time? Explain your answer.", "marks": 3, "topic": "Computer Hardware", "difficulty": "Medium"}, {"id": "AL_2023_P2_Q2", "question": "A double-sided DVD has a capacity of 8.5GB. How many CDs of 700MB storage capacity are required to store information in a full double-sided DVD?", "marks": 3, "topic": "Computer Hardware", "difficulty": "Medium"}, {"id": "AL_2023_P2_Q3", "question": "We want to build a circuit that can be used to add two 4-bit numbers. We have full adders and half adders to use as components. (a) Explain the difference between a half-adder and a full-adder. (b) Draw a block diagram of your circuit which shows how the numbers 1011 and 1110 are being added. Show and label all inputs and outputs. Use at least one half-adder and at least one full-adder.", "marks": 6, "topic": "Logic Circuits", "difficulty": "Hard"}, {"id": "AL_2023_P2_Q4", "question": "Describe TWO characteristics of a CPU that can affect the overall performance of a computer.", "marks": 4, "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "AL_2023_P2_Q5", "question": "Consider the following 8-bit binary integer which is in two's complement form: 11101010. (a) Give the denary equivalent of this binary integer. (b) Show how this binary integer would be represented in hexadecimal. (c) Show the result obtained when this binary integer is added to itself. State its decimal equivalent and explain the result.", "marks": 7, "topic": "Data Representation", "difficulty": "Hard"}, {"id": "AL_2023_P2_Q6", "question": "A RISC processor has a 32-bit architecture, with instructions that are 1-word long. The processor has 64 registers each of which is 32 bits long and supports 45 instructions (operations). Besides the operator field which holds the operation code (Opcode), each instruction has an immediate operand and two register operands. (a) Determine the minimum number of bits required for the Opcode field. (b) Determine the minimum number of bits required for each register operand field. (c) Determine the maximum number of bits available for the immediate operand field. (d) State two key characteristics of this processor's instruction set.", "marks": 8, "topic": "Computer Architecture", "difficulty": "Hard"}, {"id": "AL_2023_P2_Q7", "question": "Most conventional computers have a SISD architecture. (a) What does SISD stand for? (b) Draw a block diagram that illustrates the SISD machine architecture.", "marks": 4, "topic": "Computer Architecture", "difficulty": "Medium"}, {"id": "AL_2023_P2_Q8", "question": "With reference to operating systems: (a) Distinguish between paging and segmentation. (b) State one important relationship between the operating system and computer hardware. (c) Describe the terms Real Time processing and Batch processing using real life scenarios as examples to demonstrate their application.", "marks": 8, "topic": "Operating Systems", "difficulty": "Medium"}, {"id": "AL_2023_P2_Q9", "question": "Consider the following process handled in an operating system with the associated units of arrival and service times: PROCESS ARRIVAL TIME SERVICE TIME P1 0 7, P2 2 4, P3 4 1, P4 5 4. (a) Using the Shortest Remaining Time First scheduling policy draw a Gantt chart for these processes. (b) Calculate the average waiting time and turnaround time for the processes. (c) Calculate the throughput of the operating system.", "marks": 9, "topic": "Operating Systems", "difficulty": "Hard"}]}, "2019": {"paper_1_mcq": [{"id": "AL_2019_P1_Q1", "question": "Paper 1 MCQ questions available but content not fully extracted in current session", "options": ["Content available in PDF format on website"], "answer": "N/A", "topic": "Various", "difficulty": "Mixed"}], "paper_3_practical": [{"id": "AL_2019_P3_Q1", "question": "A two-dimensional array, A, has N rows and N columns, where N is a positive integer. The following algorithm is written to fill array A with the numbers 1,2,3,..., N².", "marks": 15, "topic": "Programming", "difficulty": "Hard"}]}}, "topic_analysis": {"o_level_topics": {"Computer Hardware": 12, "Networks": 9, "Programming": 6, "Data Representation": 5, "Web Technologies": 4, "Software Applications": 4, "Software": 3, "Security": 3, "Health and Safety": 3, "Logic Circuits": 2, "Computer Applications": 1, "Practical Applications": 1}, "a_level_topics": {"Computer Architecture": 15, "Data Representation": 6, "Advanced Computing": 4, "Operating Systems": 4, "Logic Circuits": 3, "Computer Hardware": 3, "Advanced Systems": 2, "Programming": 1}}, "difficulty_analysis": {"o_level": {"Easy": 8, "Medium": 20, "Hard": 4}, "a_level": {"Easy": 1, "Medium": 14, "Hard": 12, "Mixed": 1}}, "papers_summary": {"o_level_papers_extracted": {"2024": ["Paper 1 (13 MCQ)", "Paper 2 (8 Structural)", "Paper 3 (1 Practical)"], "2023": ["Paper 2 (4 Structural)"], "2022": ["Paper 2 (1 Structural)"], "2021": ["Paper 2 (3 Structural)"], "2019": ["Paper 2 (2 Structural)"]}, "a_level_papers_extracted": {"2024": ["Paper 1 (10 MCQ)", "Paper 2 (6 Structural)"], "2023": ["Paper 2 (9 Structural)"], "2019": ["Paper 1 (1 MCQ)", "Paper 3 (1 Practical)"]}, "total_questions_by_year": {"2024": 38, "2023": 13, "2022": 1, "2021": 3, "2019": 4}}, "recommendations": {"o_level_book_chapters": ["Chapter 1: Introduction to Computers and Computer Hardware", "Chapter 2: Computer Networks and Internet Technologies", "Chapter 3: Programming Fundamentals and Algorithms", "Chapter 4: Data Representation and Number Systems", "Chapter 5: Web Technologies and Internet Applications", "Chapter 6: Computer Software and Applications", "Chapter 7: Computer Security and Safety", "Chapter 8: Health Issues and Computer Use", "Chapter 9: Practical Applications and Projects", "Chapter 10: Examination Preparation and Past Papers"], "a_level_book_chapters": ["Chapter 1: Advanced Computer Architecture", "Chapter 2: Advanced Data Representation and Arithmetic", "Chapter 3: Parallel and Distributed Computing", "Chapter 4: Logic Circuits and Boolean Algebra", "Chapter 5: Advanced Information Systems", "Chapter 6: Assembly Language Programming", "Chapter 7: Computer System Performance", "Chapter 8: Advanced Database Systems", "Chapter 9: Artificial Intelligence and Robotics", "Chapter 10: Research Projects and Advanced Applications"]}}