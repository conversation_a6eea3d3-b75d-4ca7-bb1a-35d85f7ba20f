#!/usr/bin/env python3
"""
Cameroon GCE Computer Science Content Extraction Algorithm
=========================================================

This algorithm systematically extracts, organizes, and analyzes all MCQ and structural 
questions from Cameroon GCE Computer Science papers (2018-2024) for both O-Level and A-Level.

Author: Research Team
Date: 2024
Purpose: Comprehensive content extraction for textbook development
"""

import re
import json
import requests
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class Question:
    """Data structure for storing individual questions"""
    id: str
    type: str  # 'mcq' or 'structural'
    level: str  # 'o-level' or 'a-level'
    year: int
    paper: int  # 1, 2, or 3
    question_number: str
    content: str
    options: Optional[List[str]] = None  # For MCQ
    answer: Optional[str] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    marks: Optional[int] = None
    exam_type: Optional[str] = None  # 'gce' or 'mock'
    region: Optional[str] = None  # For mock papers: 'north-west', 'south-west', 'centre'

@dataclass
class ExamPaper:
    """Data structure for storing complete exam papers"""
    level: str
    year: int
    paper: int
    subject_code: str
    questions: List[Question]
    metadata: Dict
    exam_type: Optional[str] = None  # 'gce' or 'mock'
    region: Optional[str] = None  # For mock papers

class GCEContentExtractor:
    """Main class for extracting GCE Computer Science content"""
    
    def __init__(self):
        self.base_url = "https://cameroongcerevision.com"
        self.papers_database = []
        self.questions_database = []
        self.topic_mapping = self._initialize_topic_mapping()
        
    def _initialize_topic_mapping(self) -> Dict[str, List[str]]:
        """Initialize topic classification keywords"""
        return {
            "Computer Hardware": [
                "cpu", "processor", "memory", "ram", "hard drive", "hdd", "storage",
                "input device", "output device", "keyboard", "mouse", "monitor",
                "scanner", "printer", "motherboard", "cache"
            ],
            "Software": [
                "operating system", "application", "software", "dbms", "database",
                "compiler", "interpreter", "ide", "utility", "system software"
            ],
            "Programming": [
                "algorithm", "flowchart", "programming", "code", "variable",
                "loop", "iteration", "sequence", "choice", "control structure",
                "function", "procedure", "array", "string"
            ],
            "Data Representation": [
                "binary", "hexadecimal", "octal", "decimal", "bit", "byte",
                "kilobyte", "megabyte", "gigabyte", "terabyte", "conversion",
                "two's complement", "ones complement"
            ],
            "Networks": [
                "network", "lan", "wan", "internet", "www", "router", "modem",
                "gateway", "repeater", "client-server", "peer-to-peer",
                "protocol", "ip address", "domain"
            ],
            "Web Technologies": [
                "html", "browser", "website", "web page", "url", "http",
                "email", "username", "domain name", "web address"
            ],
            "Security": [
                "virus", "malware", "trojan", "worm", "security", "encryption",
                "password", "firewall", "antivirus", "backup"
            ],
            "Computer Architecture": [
                "von neumann", "fetch-execute", "instruction cycle", "control unit",
                "alu", "registers", "bus", "address bus", "data bus", "control bus"
            ],
            "Logic Circuits": [
                "boolean", "logic gate", "and gate", "or gate", "not gate",
                "nand", "nor", "xor", "truth table", "circuit"
            ],
            "Advanced Computing": [
                "parallel computing", "distributed computing", "simd", "mimd",
                "artificial intelligence", "machine learning", "robotics"
            ]
        }
    
    def extract_paper_urls(self) -> List[str]:
        """Extract all relevant paper URLs from the GCE Revision website"""
        urls = []

        # O-Level Computer Science URLs (2018-2024)
        o_level_patterns = [
            f"/cameroon-gce-o-level-june-{year}-computer-science-{paper}/"
            for year in range(2018, 2025)
            for paper in [1, 2, 3]
        ]

        # A-Level Computer Science URLs (2018-2024)
        a_level_patterns = [
            f"/cameroon-gce-a-level-june-{year}-computer-science-{paper}/"
            for year in range(2018, 2025)
            for paper in [1, 2, 3]
        ]

        # Alternative URL patterns
        alternative_patterns = [
            f"/cameroon-gce-advance-level-june-{year}-computer-science-{paper}/"
            for year in range(2018, 2025)
            for paper in [1, 2, 3]
        ]

        # Regional Mock Examination URLs
        mock_patterns = self._get_mock_paper_urls()

        all_patterns = o_level_patterns + a_level_patterns + alternative_patterns + mock_patterns

        for pattern in all_patterns:
            urls.append(self.base_url + pattern)

        return urls

    def _get_mock_paper_urls(self) -> List[str]:
        """Get URLs for regional mock examination papers"""
        mock_urls = []

        # North West Regional Mock URLs
        nw_mock_patterns = [
            # O-Level North West Regional Mock
            "/ordinary-level-2023-north-west-regional-mock-computer-science-1/",
            "/ordinary-level-2023-north-west-regional-mock-computer-science-2/",
            "/ordinary-level-2023-north-west-regional-mock-computer-science-3/",
            "/ordinary-level-2024-north-west-regional-mock-computer-science-1/",
            "/ordinary-level-2024-north-west-regional-mock-computer-science-2/",
            "/ordinary-level-2024-north-west-regional-mock-computer-science-3/",

            # A-Level North West Regional Mock
            "/advanced-level-2023-north-west-regional-mock-computer-science-1/",
            "/advanced-level-2023-north-west-regional-mock-computer-science-2/",
            "/advanced-level-2023-north-west-regional-mock-computer-science-3/",
            "/advanced-level-2023-north-west-regional-mock-computer-science-3-2/",
            "/advanced-level-2024-north-west-regional-mock-computer-science-1/",
            "/advanced-level-2024-north-west-regional-mock-computer-science-2/",
            "/advanced-level-2024-north-west-regional-mock-computer-science-3/",
        ]

        # South West Regional Mock URLs
        sw_mock_patterns = [
            # O-Level South West Regional Mock
            "/ordinary-level-2023-south-west-regional-mock-computer-science-1/",
            "/ordinary-level-2023-south-west-regional-mock-computer-science-2/",
            "/ordinary-level-2023-south-west-regional-mock-computer-science-3/",
            "/ordinary-level-2024-south-west-regional-mock-computer-science-1/",
            "/ordinary-level-2024-south-west-regional-mock-computer-science-2/",
            "/ordinary-level-2024-south-west-regional-mock-computer-science-3/",

            # A-Level South West Regional Mock
            "/advanced-level-2023-south-west-regional-mock-computer-science-1/",
            "/advanced-level-2023-south-west-regional-mock-computer-science-2/",
            "/advanced-level-2023-south-west-regional-mock-computer-science-3/",
            "/advanced-level-2024-south-west-regional-mock-computer-science-1/",
            "/advanced-level-2024-south-west-regional-mock-computer-science-2/",
            "/advanced-level-2024-south-west-regional-mock-computer-science-3/",
        ]

        # Centre Regional Mock URLs
        centre_mock_patterns = [
            "/centre-regional-mock-2024-advanced-level-computer-science-1/",
            "/centre-regional-mock-2024-advanced-level-computer-science-2/",
            "/centre-regional-mock-2024-advanced-level-computer-science-3/",
            "/centre-regional-mock-2024-ordinary-level-computer-science-1/",
            "/centre-regional-mock-2024-ordinary-level-computer-science-2/",
            "/centre-regional-mock-2024-ordinary-level-computer-science-3/",
        ]

        mock_urls.extend(nw_mock_patterns)
        mock_urls.extend(sw_mock_patterns)
        mock_urls.extend(centre_mock_patterns)

        return mock_urls
    
    def extract_mcq_questions(self, content: str, metadata: Dict) -> List[Question]:
        """Extract MCQ questions from paper content"""
        questions = []
        
        # Pattern for MCQ questions
        mcq_pattern = r'(\d+\.?\s*)(.*?)\s*A\s+(.*?)\s*B\s+(.*?)\s*C\s+(.*?)\s*D\s+(.*?)(?=\d+\.|\Z)'
        
        matches = re.findall(mcq_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for i, match in enumerate(matches):
            question_num, question_text, option_a, option_b, option_c, option_d = match

            # Create unique ID based on exam type
            exam_type_prefix = "MOCK" if metadata.get('exam_type') == 'mock' else "GCE"
            region_suffix = f"_{metadata.get('region', '').upper()}" if metadata.get('region') else ""

            question = Question(
                id=f"{metadata['level']}_{metadata['year']}_P{metadata['paper']}_{exam_type_prefix}_MCQ_{i+1}{region_suffix}",
                type="mcq",
                level=metadata['level'],
                year=metadata['year'],
                paper=metadata['paper'],
                question_number=question_num.strip(),
                content=question_text.strip(),
                options=[
                    f"A. {option_a.strip()}",
                    f"B. {option_b.strip()}",
                    f"C. {option_c.strip()}",
                    f"D. {option_d.strip()}"
                ],
                topic=self._classify_topic(question_text),
                exam_type=metadata.get('exam_type', 'gce'),
                region=metadata.get('region')
            )
            questions.append(question)
            
        return questions
    
    def extract_structural_questions(self, content: str, metadata: Dict) -> List[Question]:
        """Extract structural/essay questions from paper content"""
        questions = []
        
        # Pattern for structural questions
        structural_pattern = r'(\d+\.?\s*(?:\([a-z]\))?\s*)(.*?)(?=\d+\.|\Z)'
        
        matches = re.findall(structural_pattern, content, re.DOTALL)
        
        for i, match in enumerate(matches):
            question_num, question_text = match

            # Extract marks if present
            marks_match = re.search(r'\((\d+)\s*marks?\)', question_text)
            marks = int(marks_match.group(1)) if marks_match else None

            # Create unique ID based on exam type
            exam_type_prefix = "MOCK" if metadata.get('exam_type') == 'mock' else "GCE"
            region_suffix = f"_{metadata.get('region', '').upper()}" if metadata.get('region') else ""

            question = Question(
                id=f"{metadata['level']}_{metadata['year']}_P{metadata['paper']}_{exam_type_prefix}_STRUCT_{i+1}{region_suffix}",
                type="structural",
                level=metadata['level'],
                year=metadata['year'],
                paper=metadata['paper'],
                question_number=question_num.strip(),
                content=question_text.strip(),
                marks=marks,
                topic=self._classify_topic(question_text),
                exam_type=metadata.get('exam_type', 'gce'),
                region=metadata.get('region')
            )
            questions.append(question)
            
        return questions
    
    def _classify_topic(self, question_text: str) -> str:
        """Classify question into topic categories"""
        question_lower = question_text.lower()
        
        topic_scores = {}
        for topic, keywords in self.topic_mapping.items():
            score = sum(1 for keyword in keywords if keyword in question_lower)
            if score > 0:
                topic_scores[topic] = score
        
        if topic_scores:
            return max(topic_scores, key=topic_scores.get)
        return "General"
    
    def process_paper_content(self, url: str) -> Optional[ExamPaper]:
        """Process individual paper content"""
        try:
            # Extract metadata from URL
            metadata = self._extract_metadata_from_url(url)
            if not metadata:
                return None
            
            # Fetch content (simulated - in real implementation, use web scraping)
            content = self._fetch_paper_content(url)
            if not content:
                return None
            
            # Extract questions based on paper type
            questions = []
            if metadata['paper'] == 1:  # MCQ paper
                questions = self.extract_mcq_questions(content, metadata)
            else:  # Structural papers
                questions = self.extract_structural_questions(content, metadata)
            
            exam_paper = ExamPaper(
                level=metadata['level'],
                year=metadata['year'],
                paper=metadata['paper'],
                subject_code="0595" if metadata['level'] == "o-level" else "0795",
                questions=questions,
                metadata=metadata,
                exam_type=metadata.get('exam_type', 'gce'),
                region=metadata.get('region')
            )
            
            return exam_paper
            
        except Exception as e:
            logger.error(f"Error processing {url}: {str(e)}")
            return None
    
    def _extract_metadata_from_url(self, url: str) -> Optional[Dict]:
        """Extract metadata from paper URL"""
        # Regular GCE paper patterns
        gce_patterns = [
            r'/cameroon-gce-(o-level|a-level|advance-level)-june-(\d{4})-computer-science-(\d)/',
            r'/cameroon-gce-(ordinary|advanced)-level-(\d{4})-computer-science-paper-(\d)/'
        ]

        # Mock examination patterns
        mock_patterns = [
            r'/(ordinary|advanced)-level-(\d{4})-(north-west|south-west|centre)-regional-mock-computer-science-(\d)/',
            r'/(ordinary|advanced)-level-(\d{4})-(north-west|south-west|centre)-regional-mock-computer-science-(\d)-(\d)/'
        ]

        # Try regular GCE patterns first
        for pattern in gce_patterns:
            match = re.search(pattern, url)
            if match:
                level_raw, year, paper = match.groups()
                level = "o-level" if level_raw in ["o-level", "ordinary"] else "a-level"
                return {
                    "level": level,
                    "year": int(year),
                    "paper": int(paper),
                    "url": url,
                    "exam_type": "gce",
                    "region": None
                }

        # Try mock examination patterns
        for pattern in mock_patterns:
            match = re.search(pattern, url)
            if match:
                groups = match.groups()
                level_raw = groups[0]
                year = int(groups[1])
                region = groups[2]
                paper = int(groups[3])

                level = "o-level" if level_raw == "ordinary" else "a-level"
                return {
                    "level": level,
                    "year": year,
                    "paper": paper,
                    "url": url,
                    "exam_type": "mock",
                    "region": region
                }

        return None
    
    def _fetch_paper_content(self, url: str) -> Optional[str]:
        """Fetch paper content from URL (placeholder for actual implementation)"""
        # In real implementation, this would use web scraping
        # For now, return sample content based on our extracted data
        
        sample_contents = {
            "o-level-2024-1": """
            1. Browsers are used to interpret HTML code.
            A Browsers B Compilers C Interpreters D Assemblers
            
            2. The spreadsheet cell ranges A1-E5 has number of cells.
            A 25 B 30 C 5 D 15
            
            3. Arranging storage units from smallest to largest capacity:
            A Terabyte, Kilobyte, Byte, Bit
            B Kilobyte, Terabyte, Byte, Bit  
            C Bit, Byte, Kilobyte, Terabyte
            D Byte, Bit, Kilobyte, Terabyte
            """,
            
            "o-level-2024-2": """
            1. A technician wants to set up a local area network (LAN).
            Name four basic networking equipment that can be used and the principal function of each of them.
            
            2. Briefly explain the following computing terms and state an example of each.
            (a) Integrated Development Environment
            (b) Compiled language  
            (c) Interpreted language
            
            3. In the <NAME_EMAIL>
            Identify the username and the domain name.
            """,
            
            "a-level-2024-1": """
            1. It uses the same instruction sequences to process in the same way large amounts of data streamed to it.
            A Multiple-instruction, single-data
            B Multiple-instruction, multiple-data
            C Single-instruction, single-data  
            D Single-instruction, multiple-data
            
            2. Suh has data to process, at the same time, on powerful machines in various parts of the world.
            A Online Computer B Distributed Computer C Mainframe Computer D Parallel Computer
            """,
            
            "a-level-2024-2": """
            1. Using two's complement, show how the following operations on the denary numbers could be performed in a 4-bit register:
            (a) 5 - 4 (4 marks)
            (b) -3 + 6 (4 marks)

            2. Express the AND gate and the OR gate in terms of NAND gates only. (4 marks)

            3. Explain how the width of the data bus and system clock speed affect the performance of a computer system. (3 marks)
            """,

            # Mock Paper Content
            "north-west-regional-mock-2023-o-level-2": """
            1. State the difference between data validation and data verification (2 marks)

            2. Briefly explain the following concept, in relation to computer security:
            (a) Malware (2 marks)
            (b) Firewall (2 marks)

            3. Name four types of computer networks and give one advantage of each. (8 marks)
            """,

            "south-west-regional-mock-2023-o-level-2": """
            1. Briefly explain the following concept, in relation to computer security:
            (a) Virus (2 marks)
            (b) Trojan Horse (2 marks)
            (c) Worm (2 marks)

            2. Define the following terms:
            (a) LAN (2 marks)
            (b) WAN (2 marks)

            3. List four advantages of using computers in education. (4 marks)
            """,

            "north-west-regional-mock-2023-a-level-3": """
            1. Create a database table with the following fields:
            StudentID, Name, Age, Course, Grade

            2. Write SQL queries to:
            (a) Select all students with Grade 'A' (3 marks)
            (b) Update a student's course (3 marks)
            (c) Delete students with Grade 'F' (3 marks)

            3. Normalize the following table to 2NF. (6 marks)
            """
        }
        
        # Determine content key based on URL
        for key, content in sample_contents.items():
            if key.replace("-", "_") in url.replace("-", "_"):
                return content

        # Enhanced matching for mock papers
        if "north-west" in url and "2023" in url and "o-level" in url and "2" in url:
            return sample_contents["north-west-regional-mock-2023-o-level-2"]
        elif "south-west" in url and "2023" in url and "o-level" in url and "2" in url:
            return sample_contents["south-west-regional-mock-2023-o-level-2"]
        elif "north-west" in url and "2023" in url and "a-level" in url and "3" in url:
            return sample_contents["north-west-regional-mock-2023-a-level-3"]
        elif "mock" in url:
            # Return generic mock content for other mock papers
            return """
            1. Sample mock question about computer fundamentals (4 marks)

            2. Explain the following terms:
            (a) Sample term A (2 marks)
            (b) Sample term B (2 marks)

            3. List three advantages of sample technology. (6 marks)
            """

        return None
    
    def generate_comprehensive_database(self) -> Dict:
        """Generate comprehensive database of all extracted content"""
        urls = self.extract_paper_urls()
        
        database = {
            "metadata": {
                "extraction_date": datetime.now().isoformat(),
                "total_papers": 0,
                "total_questions": 0,
                "years_covered": list(range(2018, 2025)),
                "levels": ["o-level", "a-level"],
                "papers_per_level": [1, 2, 3],
                "exam_types": ["gce", "mock"],
                "regions": ["north-west", "south-west", "centre"]
            },
            "papers": [],
            "questions_by_topic": {},
            "questions_by_year": {},
            "questions_by_level": {"o-level": [], "a-level": []},
            "questions_by_exam_type": {"gce": [], "mock": []},
            "questions_by_region": {"north-west": [], "south-west": [], "centre": [], "national": []},
            "statistics": {}
        }
        
        for url in urls:
            paper = self.process_paper_content(url)
            if paper:
                database["papers"].append(asdict(paper))
                database["metadata"]["total_papers"] += 1
                
                # Organize questions
                for question in paper.questions:
                    database["metadata"]["total_questions"] += 1

                    # By topic
                    topic = question.topic or "General"
                    if topic not in database["questions_by_topic"]:
                        database["questions_by_topic"][topic] = []
                    database["questions_by_topic"][topic].append(asdict(question))

                    # By year
                    year_key = str(question.year)
                    if year_key not in database["questions_by_year"]:
                        database["questions_by_year"][year_key] = []
                    database["questions_by_year"][year_key].append(asdict(question))

                    # By level
                    database["questions_by_level"][question.level].append(asdict(question))

                    # By exam type
                    exam_type = question.exam_type or "gce"
                    database["questions_by_exam_type"][exam_type].append(asdict(question))

                    # By region
                    region = question.region or "national"
                    if region not in database["questions_by_region"]:
                        database["questions_by_region"][region] = []
                    database["questions_by_region"][region].append(asdict(question))
        
        # Generate statistics
        database["statistics"] = self._generate_statistics(database)
        
        return database
    
    def _generate_statistics(self, database: Dict) -> Dict:
        """Generate statistical analysis of the database"""
        stats = {
            "questions_per_topic": {},
            "questions_per_year": {},
            "questions_per_level": {},
            "mcq_vs_structural": {"mcq": 0, "structural": 0},
            "difficulty_distribution": {},
            "most_common_topics": []
        }
        
        # Count questions per topic
        for topic, questions in database["questions_by_topic"].items():
            stats["questions_per_topic"][topic] = len(questions)
        
        # Count questions per year
        for year, questions in database["questions_by_year"].items():
            stats["questions_per_year"][year] = len(questions)
        
        # Count questions per level
        for level, questions in database["questions_by_level"].items():
            stats["questions_per_level"][level] = len(questions)
        
        # Count MCQ vs Structural
        for paper in database["papers"]:
            for question in paper["questions"]:
                stats["mcq_vs_structural"][question["type"]] += 1
        
        # Most common topics
        topic_counts = [(topic, count) for topic, count in stats["questions_per_topic"].items()]
        stats["most_common_topics"] = sorted(topic_counts, key=lambda x: x[1], reverse=True)[:10]
        
        return stats
    
    def export_to_json(self, database: Dict, filename: str = "gce_computer_science_database.json"):
        """Export database to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(database, f, indent=2, ensure_ascii=False)
        logger.info(f"Database exported to {filename}")
    
    def generate_textbook_outline(self, database: Dict) -> Dict:
        """Generate textbook outline based on extracted content"""
        outline = {
            "o_level_book": {
                "title": "Cameroon GCE Computer Science O-Level Complete Guide",
                "chapters": [],
                "total_questions": len(database["questions_by_level"]["o-level"]),
                "recommended_pages": 300
            },
            "a_level_book": {
                "title": "Cameroon GCE Computer Science A-Level Complete Guide", 
                "chapters": [],
                "total_questions": len(database["questions_by_level"]["a-level"]),
                "recommended_pages": 400
            }
        }
        
        # Generate chapters based on topic frequency
        common_topics = database["statistics"]["most_common_topics"]
        
        for level in ["o_level", "a_level"]:
            level_key = level.replace("_", "-")
            level_questions = database["questions_by_level"][level_key]
            
            # Analyze topics for this level
            level_topics = {}
            for question in level_questions:
                topic = question["topic"]
                if topic not in level_topics:
                    level_topics[topic] = 0
                level_topics[topic] += 1
            
            # Create chapters
            chapter_num = 1
            for topic, count in sorted(level_topics.items(), key=lambda x: x[1], reverse=True):
                if count >= 3:  # Only include topics with sufficient questions
                    chapter = {
                        "number": chapter_num,
                        "title": topic,
                        "question_count": count,
                        "estimated_pages": max(15, count * 2),
                        "sample_questions": [q["content"][:100] + "..." for q in level_questions if q["topic"] == topic][:3]
                    }
                    outline[level]["chapters"].append(chapter)
                    chapter_num += 1
        
        return outline

    def extract_mock_papers_content(self) -> Dict:
        """Extract content specifically from regional mock papers"""
        mock_database = {
            "metadata": {
                "extraction_date": datetime.now().isoformat(),
                "extraction_type": "regional_mock_papers",
                "total_mock_papers": 0,
                "total_mock_questions": 0,
                "regions_covered": ["north-west", "south-west", "centre"],
                "years_covered": [2023, 2024]
            },
            "mock_papers": [],
            "questions_by_region": {
                "north-west": [],
                "south-west": [],
                "centre": []
            },
            "statistics": {
                "papers_per_region": {},
                "questions_per_region": {},
                "topics_per_region": {}
            }
        }

        mock_urls = self._get_mock_paper_urls()

        for url_pattern in mock_urls:
            full_url = self.base_url + url_pattern
            paper = self.process_paper_content(full_url)

            if paper and paper.exam_type == "mock":
                mock_database["mock_papers"].append(asdict(paper))
                mock_database["metadata"]["total_mock_papers"] += 1

                region = paper.region or "unknown"

                for question in paper.questions:
                    mock_database["metadata"]["total_mock_questions"] += 1

                    if region in mock_database["questions_by_region"]:
                        mock_database["questions_by_region"][region].append(asdict(question))

                    # Update statistics
                    if region not in mock_database["statistics"]["papers_per_region"]:
                        mock_database["statistics"]["papers_per_region"][region] = 0
                        mock_database["statistics"]["questions_per_region"][region] = 0
                        mock_database["statistics"]["topics_per_region"][region] = {}

                    mock_database["statistics"]["questions_per_region"][region] += 1

                    topic = question.topic or "General"
                    if topic not in mock_database["statistics"]["topics_per_region"][region]:
                        mock_database["statistics"]["topics_per_region"][region][topic] = 0
                    mock_database["statistics"]["topics_per_region"][region][topic] += 1

                # Initialize region statistics if not exists
                if region not in mock_database["statistics"]["papers_per_region"]:
                    mock_database["statistics"]["papers_per_region"][region] = 0
                mock_database["statistics"]["papers_per_region"][region] += 1

        return mock_database

def main():
    """Main execution function - TASK 4: Include Regional Mock Papers"""
    logger.info("Starting Cameroon GCE Computer Science Content Extraction - TASK 4")

    # Initialize extractor
    extractor = GCEContentExtractor()

    # Generate comprehensive database (includes mock papers now)
    logger.info("Generating comprehensive database with mock papers...")
    database = extractor.generate_comprehensive_database()

    # Extract mock papers specifically
    logger.info("Extracting regional mock papers...")
    mock_database = extractor.extract_mock_papers_content()

    # Export main database to JSON
    extractor.export_to_json(database)

    # Export mock papers database separately
    with open("regional_mock_papers_database.json", 'w', encoding='utf-8') as f:
        json.dump(mock_database, f, indent=2, ensure_ascii=False)
    logger.info("Mock papers database exported to regional_mock_papers_database.json")

    # Generate textbook outline
    logger.info("Generating textbook outline...")
    outline = extractor.generate_textbook_outline(database)

    # Export outline
    with open("textbook_outline.json", 'w', encoding='utf-8') as f:
        json.dump(outline, f, indent=2, ensure_ascii=False)

    # Print comprehensive summary
    print("\n" + "="*80)
    print("TASK 4 EXECUTION SUMMARY: REGIONAL MOCK PAPERS EXTRACTION")
    print("="*80)
    print(f"Total Papers Processed: {database['metadata']['total_papers']}")
    print(f"Total Questions Extracted: {database['metadata']['total_questions']}")
    print(f"O-Level Questions: {len(database['questions_by_level']['o-level'])}")
    print(f"A-Level Questions: {len(database['questions_by_level']['a-level'])}")
    print(f"GCE Questions: {len(database['questions_by_exam_type']['gce'])}")
    print(f"Mock Questions: {len(database['questions_by_exam_type']['mock'])}")
    print(f"MCQ Questions: {database['statistics']['mcq_vs_structural']['mcq']}")
    print(f"Structural Questions: {database['statistics']['mcq_vs_structural']['structural']}")

    print("\nRegional Distribution:")
    for region, questions in database['questions_by_region'].items():
        if questions:
            print(f"  {region.title()}: {len(questions)} questions")

    print("\nTop 5 Topics:")
    for topic, count in database['statistics']['most_common_topics'][:5]:
        print(f"  {topic}: {count} questions")

    print("\nMock Papers Summary:")
    print(f"  Total Mock Papers: {mock_database['metadata']['total_mock_papers']}")
    print(f"  Total Mock Questions: {mock_database['metadata']['total_mock_questions']}")
    print(f"  Regions Covered: {', '.join(mock_database['metadata']['regions_covered'])}")

    logger.info("TASK 4 - Regional Mock Papers extraction completed successfully!")

if __name__ == "__main__":
    main()
